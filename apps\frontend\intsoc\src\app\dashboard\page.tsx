'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@telesoft/ui';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from '@telesoft/d3';
import { useThreatStats } from '../../lib/hooks/useThreats';
import { useThreatsContext } from '../../lib/contexts/ThreatsContext';
import { useMemo, useCallback, useState, useEffect } from 'react';
import { ThreatIncident } from '@telesoft/types';
import {
  getMitreAttackTechniques,
  getTacticColor,
} from '../../lib/mitre-attack';
import Modal from '../../components/Modal';

// Utility function to format timestamp to human readable time
const formatTimestamp = (timestamp: number) => {
  // Handle invalid/missing timestamps
  if (!timestamp || isNaN(timestamp)) {
    return '--:--:--';
  }

  // Try different timestamp formats
  let date: Date;

  // If timestamp looks like it's already in milliseconds (13+ digits)
  if (timestamp.toString().length >= 13) {
    date = new Date(timestamp);
  } else {
    // Assume it's in seconds, convert to milliseconds
    date = new Date(timestamp * 1000);
  }

  // Check if the resulting date is valid
  if (isNaN(date.getTime())) {
    return '--:--:--';
  }

  return date.toLocaleTimeString('en-US', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

export default function Dashboard() {
  const { threats, isConnected, isConnecting, error } = useThreatsContext();
  const stats = useThreatStats(threats);

  // Debug threats data loading
  useEffect(() => {
    console.log('Dashboard threats data updated:', {
      threatsCount: threats.length,
      isConnected,
      isConnecting,
      error,
      hasData: threats.length > 0
    });
  }, [threats.length, isConnected, isConnecting, error]);

  // Pagination state for threats table
  const [currentPage, setCurrentPage] = useState(1);
  const threatsPerPage = 10;

  // Modal state for threat details
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedThreat, setSelectedThreat] = useState<ThreatIncident | null>(
    null,
  );
  const [activeTab, setActiveTab] = useState<
    'summary' | 'risk-analysis' | 'remediation' | 'mitre-attack'
  >('summary');

  // Modal state for urgency filter
  const [isUrgencyModalOpen, setIsUrgencyModalOpen] = useState(false);
  const [selectedUrgency, setSelectedUrgency] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string>('created');

  // Reset pagination when threats data changes significantly
  useEffect(() => {
    setCurrentPage(1);
  }, [threats.length]);

  // Map WebSocket states to the expected loading state
  const loading = isConnecting && threats.length === 0;

  const cardGlass = 'glass-effect border-border-primary elevation-medium';

  // Color mapping for threat severity levels - memoized to prevent recreation on every render
  const threatColorMap = useMemo(
    () => ({
      Critical: '#dc2626', // red for critical
      High: '#f59e0b', // amber for high
      Medium: '#eab308', // yellow for medium
      Low: '#22c55e', // green for low
      Info: '#3b82f6', // blue for info
    }),
    [],
  );

  // Prepare data for the doughnut chart - memoized to avoid unnecessary recalculations
  const chartData = useMemo(() => {
    return [
      { label: 'Critical', value: stats.critical },
      { label: 'High', value: stats.high },
      { label: 'Medium', value: stats.medium },
      { label: 'Low', value: stats.low },
      { label: 'Info', value: stats.info },
    ].filter((item) => item.value > 0); // Only show categories with data
  }, [
    stats.critical,
    stats.high,
    stats.medium,
    stats.low,
    stats.info,
  ]);

  // Create colors array that matches the filtered chart data
  const threatColors = useMemo(() => {
    return chartData.map(
      (item) => threatColorMap[item.label as keyof typeof threatColorMap],
    );
  }, [chartData, threatColorMap]);

  // Calculate statistics by urgency and progress - memoized callback
  const getStatsByUrgencyAndProgress = useCallback(
    (urgency: string, progress: string) => {
      return threats.filter(
        (t) =>
          t.risk_severity === urgency && t.investigation_status === progress,
      ).length;
    },
    [threats],
  );

  // Prepare timeline data for multi-line chart with 15-minute intervals over 6 hours
  const timelineDatasets = useMemo(() => {
    const now = new Date();

    // Round current time UP to the next hour (different from bar chart)
    const nextHour = new Date(now);
    nextHour.setMinutes(0, 0, 0); // Set minutes, seconds, and milliseconds to 0
    // If we're past the start of the hour, round up to the next hour
    if (
      now.getMinutes() > 0 ||
      now.getSeconds() > 0 ||
      now.getMilliseconds() > 0
    ) {
      nextHour.setTime(nextHour.getTime() + 60 * 60 * 1000); // Add 1 hour
    }

    // Create 6-hour window ending at the next hour
    const sixHoursAgo = new Date(nextHour.getTime() - 6 * 60 * 60 * 1000);

    // Create 15-minute time intervals for 6 hours (6 * 4 = 24 intervals)
    const intervals: Date[] = [];
    for (let i = 0; i < 24; i++) {
      intervals.push(new Date(sixHoursAgo.getTime() + i * 15 * 60 * 1000));
    }

    // Initialize counts for each interval
    const intervalCounts = intervals.map((date) => ({
      date,
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
      info: 0,
    }));

    // Count threats by 15-minute intervals and severity level
    threats.forEach((threat) => {
      if (threat.time) {
        // Handle different timestamp formats
        let threatDate: Date;
        if (threat.time.toString().length >= 13) {
          // Already in milliseconds
          threatDate = new Date(threat.time);
        } else {
          // In seconds, convert to milliseconds
          threatDate = new Date(threat.time * 1000);
        }

        // Find which interval this threat belongs to
        const intervalIndex = intervals.findIndex((interval, i) => {
          const nextInterval = intervals[i + 1];
          return (
            threatDate >= interval &&
            (!nextInterval || threatDate < nextInterval)
          );
        });

        if (intervalIndex >= 0) {
          const counts = intervalCounts[intervalIndex];
          switch (threat.risk_severity) {
            case 'critical':
              counts.critical++;
              break;
            case 'high':
              counts.high++;
              break;
            case 'medium':
              counts.medium++;
              break;
            case 'low':
              counts.low++;
              break;
            case 'info':
              counts.info++;
              break;
          }
        }
      }
    });

    // Convert to datasets format for MultiLineChart
    const datasets = [
      {
        label: 'Critical',
        color: threatColorMap.Critical,
        data: intervalCounts.map(({ date, critical }) => ({
          x: date,
          y: critical,
        })),
      },
      {
        label: 'High',
        color: threatColorMap.High,
        data: intervalCounts.map(({ date, high }) => ({
          x: date,
          y: high,
        })),
      },
      {
        label: 'Medium',
        color: threatColorMap.Medium,
        data: intervalCounts.map(({ date, medium }) => ({
          x: date,
          y: medium,
        })),
      },
      {
        label: 'Low',
        color: threatColorMap.Low,
        data: intervalCounts.map(({ date, low }) => ({
          x: date,
          y: low,
        })),
      },
      {
        label: 'Info',
        color: threatColorMap.Info,
        data: intervalCounts.map(({ date, info }) => ({
          x: date,
          y: info,
        })),
      },
    ];

    return datasets;
  }, [threats, threatColorMap]); // Include threatColorMap to fix exhaustive-deps warning

  // Prepare incident type data for multi-line chart over 6 hours with hourly aggregation
  const incidentTypeTimelineDatasets = useMemo(() => {
    if (!threats || threats.length === 0) {
      return [];
    }

    const now = new Date();

    // Round current time down to the nearest hour
    const currentHour = new Date(now);
    currentHour.setMinutes(0, 0, 0); // Set minutes, seconds, and milliseconds to 0

    // Create hourly intervals for 6 hours ending at the current hour
    const hourlyIntervals: { start: Date; end: Date; date: Date }[] = [];
    for (let i = 5; i >= 0; i--) {
      // Start from 5 hours ago and work forward
      const intervalStart = new Date(
        currentHour.getTime() - i * 60 * 60 * 1000,
      );
      const intervalEnd = new Date(intervalStart.getTime() + 60 * 60 * 1000);
      hourlyIntervals.push({
        start: intervalStart,
        end: intervalEnd,
        date: intervalStart,
      });
    }

    // Initialize counts for each hour interval by incident type
    const incidentTypes = ['beaconing', 'spike', 'ddos', 'outlier', 'dga'];
    const hourlyIncidentCounts = hourlyIntervals.map((interval) => ({
      ...interval,
      beaconing: 0,
      spike: 0,
      ddos: 0,
      outlier: 0,
      dga: 0,
    }));

    // Count threats by hourly intervals and incident type
    threats.forEach((threat) => {
      if (threat.time) {
        // Handle different timestamp formats
        let threatDate: Date;
        if (threat.time.toString().length >= 13) {
          // Already in milliseconds
          threatDate = new Date(threat.time);
        } else {
          // In seconds, convert to milliseconds
          threatDate = new Date(threat.time * 1000);
        }

        // Check if threat is within the 6-hour window
        const firstInterval = hourlyIntervals[0];
        const lastInterval = hourlyIntervals[hourlyIntervals.length - 1];
        if (
          threatDate >= firstInterval.start &&
          threatDate <= lastInterval.end
        ) {
          // Find which hour interval this threat belongs to
          const intervalIndex = hourlyIntervals.findIndex((interval) => {
            const inInterval =
              threatDate >= interval.start && threatDate < interval.end;
            return inInterval;
          });

          if (
            intervalIndex >= 0 &&
            incidentTypes.includes(threat.incident_type)
          ) {
            hourlyIncidentCounts[intervalIndex][
              threat.incident_type as keyof (typeof hourlyIncidentCounts)[0]
            ]++;
          }
        }
      }
    });

    // Convert to multi-line chart format (datasets)
    const typeColors = {
      beaconing: '#3b82f6', // blue
      spike: '#f59e0b', // amber
      ddos: '#dc2626', // red
      outlier: '#10b981', // emerald
      dga: '#8b5cf6', // violet
    };

    const datasets = [
      {
        label: 'Beaconing',
        color: typeColors.beaconing,
        data: hourlyIncidentCounts.map(({ date, beaconing }) => ({
          x: date,
          y: beaconing,
        })),
      },
      {
        label: 'Anomaly',
        color: typeColors.spike,
        data: hourlyIncidentCounts.map(({ date, spike }) => ({
          x: date,
          y: spike,
        })),
      },
      {
        label: 'DDoS',
        color: typeColors.ddos,
        data: hourlyIncidentCounts.map(({ date, ddos }) => ({
          x: date,
          y: ddos,
        })),
      },
      {
        label: 'Outlier',
        color: typeColors.outlier,
        data: hourlyIncidentCounts.map(({ date, outlier }) => ({
          x: date,
          y: outlier,
        })),
      },
      {
        label: 'DGA',
        color: typeColors.dga,
        data: hourlyIncidentCounts.map(({ date, dga }) => ({
          x: date,
          y: dga,
        })),
      },
    ];

    return datasets;
  }, [threats]); // Only recalculate when threats data changes

  // Prepare radar chart data for investigation outcomes
  const investigationOutcomesData = useMemo(() => {
    // Only calculate if we have threats data
    if (!threats || threats.length === 0) {
      return [];
    }

    // Calculate different investigation metrics
    const totalThreats = threats.length;
    const completeCount = threats.filter(
      (t) => t.investigation_status === 'complete',
    ).length;
    const failedCount = threats.filter(
      (t) => t.investigation_status === 'failed',
    ).length;

    // Calculate actual actions taken based on remediation_actions field
    const actionsCount = threats.filter((t) =>
      t.remediation_actions &&
      t.remediation_actions.trim().length > 0 &&
      t.remediation_actions.trim() !== 'N/A' &&
      t.remediation_actions.trim() !== 'None' &&
      t.remediation_actions.trim() !== '-'
    ).length;

    // Calculate investigations in progress (running status)
    const runningCount = threats.filter((t) =>
      t.investigation_status === 'running',
    ).length;

    // Reorder data to compensate for RadarChart's 45-degree rotation
    // The chart rotates by 45 degrees, so we need to shift positions
    const radarData = [
      {
        axis: 'Investigations Running',
        value:
          totalThreats > 0
            ? Math.round((runningCount / totalThreats) * 100)
            : 0,
        maxValue: 100,
      },
      {
        axis: 'Investigations Complete',
        value:
          totalThreats > 0
            ? Math.round((completeCount / totalThreats) * 100)
            : 0,
        maxValue: 100,
      },
      {
        axis: 'Actions Taken',
        value:
          totalThreats > 0
            ? Math.round((actionsCount / totalThreats) * 100)
            : 0,
        maxValue: 100,
      },
      {
        axis: 'Investigations Failed',
        value:
          totalThreats > 0 ? Math.round((failedCount / totalThreats) * 100) : 0,
        maxValue: 100,
      },
    ];



    // Debug the actual calculations
    console.log('Radar Chart Calculations:', {
      totalThreats,
      completeCount,
      failedCount,
      runningCount,
      actionsCount,
      percentages: {
        complete: Math.round((completeCount / totalThreats) * 100),
        failed: Math.round((failedCount / totalThreats) * 100),
        running: Math.round((runningCount / totalThreats) * 100),
        actions: Math.round((actionsCount / totalThreats) * 100)
      },
      radarDataValues: radarData.map(d => `${d.axis}: ${d.value}%`)
    });

    return radarData;
  }, [threats]);

  const urgencyLevels = [
    {
      level: 'critical',
      label: 'Critical',
      bgColor: 'bg-cyber-danger-500/20',
      textColor: 'text-cyber-danger-400',
      borderColor: 'border-cyber-danger-500/50',
    },
    {
      level: 'high',
      label: 'High',
      bgColor: 'bg-cyber-amber-500/20',
      textColor: 'text-cyber-amber-400',
      borderColor: 'border-cyber-amber-500/50',
    },
    {
      level: 'medium',
      label: 'Medium',
      bgColor: 'bg-cyber-warning-500/20',
      textColor: 'text-cyber-warning-400',
      borderColor: 'border-cyber-warning-500/50',
    },
    {
      level: 'low',
      label: 'Low',
      bgColor: 'bg-cyber-matrix-500/20',
      textColor: 'text-cyber-matrix-400',
      borderColor: 'border-cyber-matrix-500/50',
    },
    {
      level: 'info',
      label: 'Info',
      bgColor: 'bg-blue-500/20',
      textColor: 'text-blue-400',
      borderColor: 'border-blue-500/50',
    },

  ];

  const progressColumns = [
    { key: 'created', label: 'Created' },
    { key: 'running', label: 'Running' },
    { key: 'complete', label: 'Complete' },
    { key: 'failed', label: 'Failed' },
  ];

  // Pagination calculations for threats table
  const sortedThreats = useMemo(() => {
    return threats.sort((a, b) => (b.time || 0) - (a.time || 0));
  }, [threats]);

  const totalPages = Math.ceil(sortedThreats.length / threatsPerPage);
  const startIndex = (currentPage - 1) * threatsPerPage;
  const endIndex = startIndex + threatsPerPage;
  const currentThreats = sortedThreats.slice(startIndex, endIndex);

  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  // Handle viewing threat details
  const handleViewDetail = (threat: ThreatIncident) => {
    setSelectedThreat(threat);
    setActiveTab('summary'); // Reset to summary tab
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedThreat(null);
  };

  // Handle clicking urgency level row to filter threats
  const handleUrgencyClick = (urgencyLevel: string) => {
    setSelectedUrgency(urgencyLevel);
    setSelectedStatus('created'); // Always start with "Created" filter
    setIsUrgencyModalOpen(true);
    // Close individual threat modal if open
    if (isModalOpen) {
      setIsModalOpen(false);
      setSelectedThreat(null);
    }
  };

  // Handle clicking total row to show all threats
  const handleTotalClick = () => {
    setSelectedUrgency('total'); // Use 'total' as a special urgency level
    setSelectedStatus('created'); // Always start with "Created" filter
    setIsUrgencyModalOpen(true);
    // Close individual threat modal if open
    if (isModalOpen) {
      setIsModalOpen(false);
      setSelectedThreat(null);
    }
  };

  const handleCloseUrgencyModal = () => {
    setIsUrgencyModalOpen(false);
    setSelectedUrgency(null);
    setSelectedStatus('created'); // Reset to default
  };

  // Handle clicking status filter in urgency modal
  const handleStatusFilterClick = (status: string) => {
    setSelectedStatus(status);
  };

  // Filter threats by selected urgency level and status
  const filteredThreatsByUrgency = useMemo(() => {
    if (!selectedUrgency) return [];

    if (selectedUrgency === 'total') {
      // For total row, show all threats with the selected status
      return threats
        .filter((threat) => threat.investigation_status === selectedStatus)
        .sort((a, b) => (b.time || 0) - (a.time || 0));
    }

    return threats
      .filter(
        (threat) =>
          threat.risk_severity === selectedUrgency &&
          threat.investigation_status === selectedStatus,
      )
      .sort((a, b) => (b.time || 0) - (a.time || 0));
  }, [threats, selectedUrgency, selectedStatus]);

  return (
    <div className="min-h-screen bg-background-primary p-8 pt-24">
      <div className="mx-auto max-w-7xl">
        {/* Threat Overview Card - Full Width */}
        <div className="mb-2">
          <Card className={cardGlass}>
            <CardHeader className="pb-0 px-4 pt-4">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-semibold text-text-primary">
                  Incident Summary
                </h3>
                <div className="flex items-center space-x-2">
                  <div
                    className={`w-2 h-2 rounded-full animate-pulse ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}
                  ></div>
                  <span className="text-xs text-text-secondary">
                    {isConnected ? 'Live' : 'Offline'}
                  </span>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-2">
              {loading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-primary"></div>
                </div>
              ) : error ? (
                <div className="text-center text-red-400 h-64 flex items-center justify-center">
                  <p>Failed to load threat data</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Single Row: Pie Chart, Table, and Investigation Outcomes Chart */}
                  <div className="flex flex-col xl:flex-row gap-4 items-center">
                    {/* Pie Chart Section */}
                    <div className="flex-shrink-0 flex items-center justify-center h-64">
                      {chartData.length > 0 ? (
                        <PieChart
                          data={chartData}
                          width={240}
                          height={240}
                          innerRadius={60}
                          outerRadius={120}
                          colors={threatColors}
                          centerLabel="Incidents"
                          showLabels={false}
                          showLegend={false}
                        />
                      ) : (
                        <div className="text-center text-muted-foreground h-64 w-64 flex items-center justify-center">
                          <p>No threat data available</p>
                        </div>
                      )}
                    </div>

                    {/* Statistics Table Section */}
                    <div className="flex-1 min-w-0 h-64 flex flex-col justify-center">
                      <div className="overflow-x-auto px-2">
                        <table className="w-full text-xs">
                          <thead>
                            <tr className="border-b border-border-primary">
                              <th className="text-left py-1 px-1 font-medium text-text-secondary">
                                Urgency
                              </th>
                              {progressColumns.map((col) => (
                                <th
                                  key={col.key}
                                  className="text-center py-1 px-1 font-medium text-text-secondary"
                                >
                                  {col.label}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody>
                            {urgencyLevels.map((urgency) => (
                              <tr
                                key={urgency.level}
                                className={`border-b border-border-secondary/50 ${urgency.bgColor} ${urgency.borderColor} border-l-2 cursor-pointer hover:bg-opacity-30 transition-colors duration-150`}
                              >
                                <td
                                  className={`py-1 px-1 font-semibold ${urgency.textColor}`}
                                >
                                  {urgency.label}
                                </td>
                                {progressColumns.map((col) => (
                                  <td
                                    key={col.key}
                                    className="text-center py-1 px-1 text-text-primary font-medium cursor-pointer"
                                    onClick={() => {
                                      setSelectedUrgency(urgency.level);
                                      setSelectedStatus(col.key);
                                      setIsUrgencyModalOpen(true);
                                      if (isModalOpen) {
                                        setIsModalOpen(false);
                                        setSelectedThreat(null);
                                      }
                                    }}
                                  >
                                    {getStatsByUrgencyAndProgress(
                                      urgency.level,
                                      col.key,
                                    )}
                                  </td>
                                ))}
                              </tr>
                            ))}
                            {/* Total Row */}
                            <tr
                              key="total-row"
                              className="border-t-2 border-border-accent bg-background-secondary/50 cursor-pointer hover:bg-background-secondary/70 transition-colors duration-150"
                            >
                              <td className="py-1 px-1 font-bold text-text-accent" onClick={() => {
                                setSelectedUrgency('total');
                                setSelectedStatus('created');
                                setIsUrgencyModalOpen(true);
                                if (isModalOpen) {
                                  setIsModalOpen(false);
                                  setSelectedThreat(null);
                                }
                              }}>
                                Total
                              </td>
                              {progressColumns.map((col) => (
                                <td
                                  key={`total-${col.key}`}
                                  className="text-center py-1 px-1 font-bold text-text-primary cursor-pointer"
                                  onClick={() => {
                                    setSelectedUrgency('total');
                                    setSelectedStatus(col.key);
                                    setIsUrgencyModalOpen(true);
                                    if (isModalOpen) {
                                      setIsModalOpen(false);
                                      setSelectedThreat(null);
                                    }
                                  }}
                                >
                                  {threats.filter((t) => t.investigation_status === col.key).length}
                                </td>
                              ))}
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>

                    {/* Investigation Outcomes Radar Chart Section */}
                    <div className="flex-shrink-0 w-80 h-64 flex flex-col justify-center">
                      <div className="flex justify-end">
                        {investigationOutcomesData.length > 0 ? (
                          <RadarChart
                            data={investigationOutcomesData}
                            width={320}
                            height={320}
                            color="#3b82f6"
                            fillOpacity={0.2}
                            strokeWidth={2}
                            showLabels={true}
                            showGrid={true}
                            showValues={true}
                            dotRadius={4}
                            levels={5}
                          />
                        ) : (
                          <div className="text-center text-muted-foreground h-64 w-64 flex items-center justify-center">
                            <p>No investigation data available</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Bottom Section - Timeline Chart, Bar Chart and Threats Table */}
        <div className="flex flex-col lg:flex-row gap-2 lg:items-stretch">
          {/* Left Side - Timeline Chart */}
          <div className="flex-1 lg:max-w-[33.33%]">
            <Card className={`${cardGlass} h-full`}>
              <CardContent className="p-2">
                {loading ? (
                  <div className="flex items-center justify-center h-48">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-primary"></div>
                  </div>
                ) : error ? (
                  <div className="text-center text-red-400 h-48 flex items-center justify-center">
                    <p>Failed to load data</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium text-text-primary mb-2">
                      Incident Timeline
                    </h4>
                    <div className="flex justify-center">
                      {timelineDatasets.length > 0 ? (
                        <MultiLineChart
                          datasets={timelineDatasets}
                          width={425}
                          height={240}
                          margin={{ top: 35, right: 30, bottom: 60, left: 40 }}
                          strokeWidth={2}
                          showLegend={true}
                          legendLocation="top"
                          legendAlign="horizontal"
                          yMin={0}
                        />
                      ) : (
                        <div className="text-center text-muted-foreground h-48 flex items-center justify-center">
                          <p>No timeline data available</p>
                        </div>
                      )}
                    </div>

                    {/* Events Per Detection Type Multi-Line Chart */}
                    <div>
                      <h4 className="text-sm font-medium text-text-primary mb-2">
                        Events Per Detection Type
                      </h4>
                      <div className="flex justify-center">
                        {incidentTypeTimelineDatasets.length > 0 ? (
                          <MultiLineChart
                            datasets={incidentTypeTimelineDatasets}
                            width={425}
                            height={240}
                            margin={{
                              top: 35,
                              right: 30,
                              bottom: 60,
                              left: 40,
                            }}
                            showLegend={true}
                            legendLocation="top"
                            legendAlign="horizontal"
                            yMin={0}
                          />
                        ) : (
                          <div className="text-center text-muted-foreground h-48 flex items-center justify-center">
                            <div>
                              <p>No incident data available</p>
                              <p className="text-xs mt-2">
                                Datasets length:{' '}
                                {incidentTypeTimelineDatasets.length}
                              </p>
                              <p className="text-xs">
                                Total incidents:{' '}
                                {incidentTypeTimelineDatasets.reduce(
                                  (sum, dataset) =>
                                    sum +
                                    dataset.data.reduce(
                                      (acc, point) => acc + point.y,
                                      0,
                                    ),
                                  0,
                                )}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right Side - Threats Detected Table */}
          <div className="flex-1 lg:max-w-[66.67%]">
            <Card className={`${cardGlass} flex flex-col h-full`}>
              <CardHeader className="pb-0 px-4 pt-4 flex-shrink-0">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-semibold text-text-primary">
                    Incidents
                  </h3>
                  {sortedThreats.length > 0 && (
                    <span className="text-sm text-text-secondary">
                      {sortedThreats.length} total
                    </span>
                  )}
                </div>
              </CardHeader>
              <CardContent className="p-2 flex-1 flex flex-col min-h-0">
                {loading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-primary"></div>
                  </div>
                ) : error ? (
                  <div className="text-center text-red-400 h-32 flex items-center justify-center">
                    <p>Failed to load threat data</p>
                  </div>
                ) : (
                  <>
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b border-border-primary">
                            <th className="text-left py-1 px-3 font-medium text-text-secondary">
                              Time
                            </th>
                            <th className="text-left py-1 px-3 font-medium text-text-secondary">
                              Detection Type
                            </th>
                            <th className="text-left py-1 px-3 font-medium text-text-secondary">
                              Description
                            </th>
                            <th className="text-left py-1 px-3 font-medium text-text-secondary">
                              Severity
                            </th>
                            <th className="text-left py-1 px-3 font-medium text-text-secondary">
                              Status
                            </th>
                            <th className="text-left py-1 px-3 font-medium text-text-secondary w-40"></th>
                          </tr>
                        </thead>
                        <tbody>
                          {currentThreats.length > 0 ? (
                            currentThreats.map((threat, index) => {
                              const isLastRow =
                                index === currentThreats.length - 1;
                              return (
                                <tr
                                  key={threat.uid}
                                  className={`${isLastRow ? '' : 'border-b border-border-secondary/50'} hover:bg-background-hover/30`}
                                >
                                  <td className="py-1 px-3 font-medium text-text-primary">
                                    {formatTimestamp(threat.time)}
                                  </td>
                                  <td className="py-1 px-3 text-text-primary capitalize">
                                    {threat.incident_type}
                                  </td>
                                  <td className="py-1 px-3 text-text-secondary text-sm max-w-xs truncate">
                                    {threat.summary || 'No description available'}
                                  </td>
                                  <td className="py-1 px-3">
                                    <span
                                      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${threat.risk_severity === 'critical'
                                        ? 'bg-cyber-danger-500/20 text-cyber-danger-400 border border-cyber-danger-500/50'
                                        : threat.risk_severity === 'high'
                                          ? 'bg-cyber-amber-500/20 text-cyber-amber-400 border border-cyber-amber-500/50'
                                          : threat.risk_severity === 'medium'
                                            ? 'bg-cyber-warning-500/20 text-cyber-warning-400 border border-cyber-warning-500/50'
                                            : threat.risk_severity === 'low'
                                              ? 'bg-green-500/20 text-green-400 border border-green-500/50'
                                              : threat.risk_severity === 'info'
                                                ? 'bg-blue-500/20 text-blue-400 border border-blue-500/50'
                                                : threat.risk_severity === 'unknown'
                                                  ? 'bg-gray-900/20 text-gray-900 border border-gray-900/50'
                                                  : 'bg-cyber-matrix-500/20 text-cyber-matrix-400 border border-cyber-matrix-500/50'
                                        }`}
                                    >
                                      {threat.risk_severity
                                        .charAt(0)
                                        .toUpperCase() +
                                        threat.risk_severity.slice(1)}
                                    </span>
                                  </td>
                                  <td className="py-1 px-3">
                                    <span
                                      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${threat.investigation_status === 'created'
                                        ? 'bg-green-500/20 text-green-400 border border-green-500/50'
                                        : threat.investigation_status === 'running'
                                          ? 'bg-blue-500/20 text-blue-400 border border-blue-500/50'
                                          : threat.investigation_status === 'complete'
                                            ? 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/50'
                                            : threat.investigation_status === 'failed'
                                              ? 'bg-red-500/20 text-red-400 border border-red-500/50'
                                              : 'bg-gray-500/20 text-gray-400 border border-gray-500/50'
                                        }`}
                                    >
                                      {threat.investigation_status
                                        .charAt(0)
                                        .toUpperCase() +
                                        threat.investigation_status.slice(1)}
                                    </span>
                                  </td>
                                  <td className="py-1 px-3">
                                    <div className="flex space-x-1">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="whitespace-nowrap text-xs"
                                        onClick={() => handleViewDetail(threat)}
                                      >
                                        View Detail
                                      </Button>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="whitespace-nowrap text-xs"
                                        onClick={() => handleViewDetail(threat)}
                                      >
                                        Silence
                                      </Button>
                                    </div>
                                  </td>
                                </tr>
                              );
                            })
                          ) : (
                            <tr>
                              <td
                                colSpan={6}
                                className="py-4 text-center text-text-secondary"
                              >
                                No threats detected
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>

                    {/* Pagination Controls */}
                    {totalPages > 1 && (
                      <div className="flex items-center justify-between mt-2 pt-2 border-t border-border-secondary/50">
                        <div className="text-sm text-text-secondary">
                          Showing {startIndex + 1} to{' '}
                          {Math.min(endIndex, sortedThreats.length)} of{' '}
                          {sortedThreats.length} threats
                        </div>
                        <div className="flex items-center space-x-2">
                          {/* Go to first page */}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => goToPage(1)}
                            disabled={currentPage === 1}
                            className="w-10 h-10 p-0"
                            title="Go to first page"
                          >
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <polyline points="11,17 6,12 11,7"></polyline>
                              <polyline points="18,17 13,12 18,7"></polyline>
                            </svg>
                          </Button>

                          {/* Previous page */}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => goToPage(currentPage - 1)}
                            disabled={currentPage === 1}
                            className="w-10 h-10 p-0"
                            title="Previous page"
                          >
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <polyline points="15,18 9,12 15,6"></polyline>
                            </svg>
                          </Button>

                          {/* Page numbers */}
                          <div className="flex items-center space-x-1">
                            {Array.from(
                              { length: Math.min(5, totalPages) },
                              (_, i) => {
                                let pageNum;
                                if (totalPages <= 5) {
                                  pageNum = i + 1;
                                } else {
                                  // Show current page and 2 pages on each side when possible
                                  const start = Math.max(
                                    1,
                                    Math.min(currentPage - 2, totalPages - 4),
                                  );
                                  pageNum = start + i;
                                }

                                return (
                                  <Button
                                    key={pageNum}
                                    variant={
                                      currentPage === pageNum
                                        ? 'primary'
                                        : 'ghost'
                                    }
                                    size="sm"
                                    onClick={() => goToPage(pageNum)}
                                    className="w-10 h-10 p-0"
                                  >
                                    {pageNum}
                                  </Button>
                                );
                              },
                            )}
                          </div>

                          {/* Next page */}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => goToPage(currentPage + 1)}
                            disabled={currentPage === totalPages}
                            className="w-10 h-10 p-0"
                            title="Next page"
                          >
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <polyline points="9,18 15,12 9,6"></polyline>
                            </svg>
                          </Button>

                          {/* Go to last page */}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => goToPage(totalPages)}
                            disabled={currentPage === totalPages}
                            className="w-10 h-10 p-0"
                            title="Go to last page"
                          >
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <polyline points="13,17 18,12 13,7"></polyline>
                              <polyline points="6,17 11,12 6,7"></polyline>
                            </svg>
                          </Button>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Threat Detail Modal */}
        {isModalOpen && selectedThreat && (
          <Modal
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            type={selectedThreat.incident_type}
            severity={selectedThreat.risk_severity}
            time={formatTimestamp(selectedThreat.time)}
            tabs={[
              {
                id: 'summary',
                label: 'Summary',
                active: activeTab === 'summary',
                onClick: () => setActiveTab('summary'),
              },
              {
                id: 'risk-analysis',
                label: 'Risk Analysis',
                active: activeTab === 'risk-analysis',
                onClick: () => setActiveTab('risk-analysis'),
              },
              {
                id: 'remediation',
                label: 'Remediation Advice',
                active: activeTab === 'remediation',
                onClick: () => setActiveTab('remediation'),
              },
              {
                id: 'mitre-attack',
                label: 'Mitre ATT&CK',
                active: activeTab === 'mitre-attack',
                onClick: () => setActiveTab('mitre-attack'),
              },
            ]}
          >
            <div className="min-h-[200px] p-6">
              {activeTab === 'summary' && (
                <div>
                  {selectedThreat.summary ? (
                    <div className="text-text-secondary whitespace-pre-wrap leading-relaxed">
                      {selectedThreat.summary}
                    </div>
                  ) : (
                    <div className="text-text-secondary italic">
                      No summary available for this threat.
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'risk-analysis' && (
                <div>
                  {selectedThreat.risk_message ? (
                    <div className="text-text-secondary whitespace-pre-wrap leading-relaxed">
                      {selectedThreat.risk_message}
                    </div>
                  ) : (
                    <div className="text-text-secondary italic">
                      No risk analysis available for this threat.
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'remediation' && (
                <div>
                  {selectedThreat.remediation_actions ? (
                    <div className="text-text-secondary whitespace-pre-wrap leading-relaxed">
                      {selectedThreat.remediation_actions}
                    </div>
                  ) : (
                    <div className="text-text-secondary italic">
                      No remediation advice available for this threat.
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'mitre-attack' && (
                <div className="space-y-6">
                  {/* Current TTPs Section */}
                  <div>
                    <h3 className="text-base font-semibold text-text-primary mb-4 flex items-center gap-2">
                      <span className="w-3 h-3 bg-cyber-danger-500 rounded-full"></span>
                      Current TTPs Detected
                    </h3>
                    {selectedThreat.current_ttps &&
                      selectedThreat.current_ttps.length > 0 ? (
                      <div className="space-y-3">
                        {getMitreAttackTechniques(
                          selectedThreat.current_ttps,
                        ).map((ttp, index) => (
                          <div
                            key={index}
                            className="bg-surface-secondary/30 border border-border-primary/30 rounded-lg p-4"
                          >
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex items-center gap-3">
                                <span className="font-mono text-sm bg-cyber-danger-500/20 text-cyber-danger-400 px-2 py-1 rounded border border-cyber-danger-500/30">
                                  {ttp.id}
                                </span>
                                <span
                                  className={`font-semibold ${ttp.found ? 'text-text-primary' : 'text-text-secondary'}`}
                                >
                                  {ttp.name}
                                </span>
                              </div>
                              {!ttp.found && (
                                <span className="text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded border border-yellow-500/30">
                                  Unknown
                                </span>
                              )}
                            </div>
                            <div className="mb-2">
                              <span
                                className={`inline-block text-xs px-2 py-1 rounded border ${getTacticColor(ttp.tactic)}`}
                              >
                                {ttp.tactic}
                              </span>
                            </div>
                            <p className="text-text-secondary text-sm leading-relaxed">
                              {ttp.description}
                            </p>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-text-subtle italic bg-surface-secondary/20 rounded-lg border border-border-muted">
                        No current TTPs detected for this threat.
                      </div>
                    )}
                  </div>

                  {/* Future TTPs Section */}
                  <div>
                    <h3 className="text-base font-semibold text-text-primary mb-4 flex items-center gap-2">
                      <span className="w-3 h-3 bg-cyber-warning-500 rounded-full"></span>
                      Potential Future TTPs
                    </h3>
                    {selectedThreat.future_ttps &&
                      selectedThreat.future_ttps.length > 0 ? (
                      <div className="space-y-3">
                        {getMitreAttackTechniques(
                          selectedThreat.future_ttps,
                        ).map((ttp, index) => (
                          <div
                            key={index}
                            className="bg-surface-secondary/20 border border-border-primary/20 rounded-lg p-4"
                          >
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex items-center gap-3">
                                <span className="font-mono text-sm bg-cyber-warning-500/20 text-cyber-warning-400 px-2 py-1 rounded border border-cyber-warning-500/30">
                                  {ttp.id}
                                </span>
                                <span
                                  className={`font-semibold ${ttp.found ? 'text-text-primary' : 'text-text-secondary'}`}
                                >
                                  {ttp.name}
                                </span>
                              </div>
                              {!ttp.found && (
                                <span className="text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded border border-yellow-500/30">
                                  Unknown
                                </span>
                              )}
                            </div>
                            <div className="mb-2">
                              <span
                                className={`inline-block text-xs px-2 py-1 rounded border ${getTacticColor(ttp.tactic)}`}
                              >
                                {ttp.tactic}
                              </span>
                            </div>
                            <p className="text-text-secondary text-sm leading-relaxed">
                              {ttp.description}
                            </p>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-text-subtle italic bg-surface-secondary/20 rounded-lg border border-border-muted">
                        No future TTPs predicted for this threat.
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </Modal>
        )}

        {/* Urgency Filter Modal */}
        {isUrgencyModalOpen && selectedUrgency && (
          <Modal
            isOpen={isUrgencyModalOpen}
            onClose={handleCloseUrgencyModal}
            type={
              selectedUrgency === 'total'
                ? 'All Severity Threats'
                : `${selectedUrgency.charAt(0).toUpperCase() + selectedUrgency.slice(1)} Severity Threats`
            }
            severity={selectedUrgency === 'total' ? undefined : selectedUrgency}
            time=""
            tabs={[]}
          >
            <div className="p-6">
              <div className="mb-6">
                {/* Status Distribution Timeline */}
                <div className="mb-4">
                  <div className="flex items-center justify-between relative px-4">
                    {/* Timeline Line */}
                    <div className="absolute top-6 left-8 right-8 h-0.5 bg-border-secondary/30 z-0"></div>

                    {progressColumns.map((col, index) => {
                      const count =
                        selectedUrgency === 'total'
                          ? threats.filter(
                            (threat) =>
                              threat.investigation_status === col.key,
                          ).length
                          : threats.filter(
                            (threat) =>
                              threat.risk_severity === selectedUrgency &&
                              threat.investigation_status === col.key,
                          ).length;
                      const hasCount = count > 0;
                      const isSelected = selectedStatus === col.key;

                      return (
                        <div
                          key={col.key}
                          className="flex flex-col items-center relative z-10 flex-1"
                        >
                          {/* Timeline Node - Clickable */}
                          <button
                            onClick={() => handleStatusFilterClick(col.key)}
                            className={`w-10 h-10 rounded-full border-2 flex items-center justify-center mb-3 transition-all duration-200 hover:scale-110 cursor-pointer ${isSelected
                              ? col.key === 'created'
                                ? 'bg-green-500 border-green-500 text-white shadow-lg shadow-green-500/25'
                                : col.key === 'running'
                                  ? 'bg-blue-500 border-blue-500 text-white shadow-lg shadow-blue-500/25'
                                  : col.key === 'complete'
                                    ? 'bg-emerald-500 border-emerald-500 text-white shadow-lg shadow-emerald-500/25'
                                    : col.key === 'failed'
                                      ? 'bg-red-500 border-red-500 text-white shadow-lg shadow-red-500/25'
                                      : 'bg-gray-500 border-gray-500 text-white shadow-lg shadow-gray-500/25'
                              : hasCount
                                ? col.key === 'created'
                                  ? 'bg-green-500/20 border-green-500 text-green-400 hover:bg-green-500/30'
                                  : col.key === 'running'
                                    ? 'bg-blue-500/20 border-blue-500 text-blue-400 hover:bg-blue-500/30'
                                    : col.key === 'complete'
                                      ? 'bg-emerald-500/20 border-emerald-500 text-emerald-400 hover:bg-emerald-500/30'
                                      : col.key === 'failed'
                                        ? 'bg-red-500/20 border-red-500 text-red-400 hover:bg-red-500/30'
                                        : 'bg-gray-500/20 border-gray-500 text-gray-400 hover:bg-gray-500/30'
                                : 'bg-background-secondary border-border-secondary text-text-subtle hover:bg-background-tertiary'
                              }`}
                          >
                            <span className="text-sm font-bold">{count}</span>
                          </button>

                          {/* Status Label */}
                          <div
                            className={`text-center min-h-[2.5rem] flex flex-col justify-center ${isSelected
                              ? 'text-text-primary font-semibold'
                              : hasCount
                                ? 'text-text-primary'
                                : 'text-text-subtle'
                              }`}
                          >
                            <div className="text-sm font-medium whitespace-nowrap">
                              {col.label}
                            </div>
                            {isSelected && (
                              <div className="text-xs text-accent-primary mt-1">
                                Active Filter
                              </div>
                            )}
                          </div>

                          {/* Connecting Arrow (except for last item) */}
                          {index < progressColumns.length - 1 && (
                            <div className="absolute top-6 -right-4 w-8 h-1 bg-border-secondary z-0">
                              <div className="absolute -right-2 -top-1.5 w-4 h-4 border-r-2 border-t-2 border-border-secondary transform rotate-45"></div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>

                <p className="text-text-secondary text-sm">
                  Showing {filteredThreatsByUrgency.length} {selectedStatus}{' '}
                  threat{filteredThreatsByUrgency.length !== 1 ? 's' : ''}{' '}
                  {selectedUrgency === 'total'
                    ? 'with all severities'
                    : `with ${selectedUrgency} severity`}
                </p>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-background-primary border-b border-border-primary">
                    <tr>
                      <th className="text-left py-3 px-4 font-medium text-text-secondary">
                        Time
                      </th>
                      <th className="text-left py-3 px-4 font-medium text-text-secondary">
                        Threat Type
                      </th>
                      <th className="text-left py-3 px-4 font-medium text-text-secondary">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredThreatsByUrgency.length > 0 ? (
                      filteredThreatsByUrgency.map((threat) => (
                        <tr
                          key={threat.uid}
                          className="border-b border-border-secondary/50 hover:bg-background-hover/30"
                        >
                          <td className="py-3 px-4 font-medium text-text-primary">
                            {formatTimestamp(threat.time)}
                          </td>
                          <td className="py-3 px-4 text-text-primary capitalize">
                            {threat.incident_type}
                          </td>
                          <td className="py-3 px-4">
                            <span
                              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${threat.investigation_status === 'created'
                                ? 'bg-green-500/20 text-green-400 border border-green-500/50'
                                : threat.investigation_status === 'running'
                                  ? 'bg-blue-500/20 text-blue-400 border border-blue-500/50'
                                  : threat.investigation_status === 'complete'
                                    ? 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/50'
                                    : threat.investigation_status === 'failed'
                                      ? 'bg-red-500/20 text-red-400 border border-red-500/50'
                                      : 'bg-gray-500/20 text-gray-400 border border-gray-500/50'
                                }`}
                            >
                              {threat.investigation_status
                                .charAt(0)
                                .toUpperCase() +
                                threat.investigation_status.slice(1)}
                            </span>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan={4}
                          className="py-8 text-center text-text-secondary"
                        >
                          No{' '}
                          {selectedUrgency === 'total'
                            ? 'threats'
                            : `${selectedUrgency} severity threats`}{' '}
                          found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </Modal>
        )}
      </div>
    </div>
  );
}
